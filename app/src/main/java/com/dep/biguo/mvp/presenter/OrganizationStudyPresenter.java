package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.OrganizationStudyBean;
import com.dep.biguo.mvp.contract.OrganizationStudyContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class OrganizationStudyPresenter extends BasePresenter<OrganizationStudyContract.Model, OrganizationStudyContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public OrganizationStudyPresenter(OrganizationStudyContract.Model model, OrganizationStudyContract.View rootView) {
        super(model, rootView);
    }

    public void getClassType(int id) {
        mModel.getClassType(id)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<OrganizationStudyBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<OrganizationStudyBean>> s) {
                        if (s.isSuccess()) {
                            if(AppUtil.isEmpty(s.getData())){
                                mRootView.showEmptyView();
                                mRootView.getClassTypeSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getClassTypeSuccess(s.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
