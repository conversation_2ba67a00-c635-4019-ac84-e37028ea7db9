package com.dep.biguo.mvp.ui.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.os.Build;
import android.view.Window;
import android.view.WindowManager;
import android.graphics.Color;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.GridLayoutManager;

import com.dep.biguo.R;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.databinding.MallHomeFragmentBinding;
import com.dep.biguo.mvp.ui.adapter.MallProductAdapter;
import com.dep.biguo.mvp.ui.activity.ShopDetailActivity;
import com.dep.biguo.mvp.ui.activity.TextBooksActivity;
import com.dep.biguo.mvp.ui.activity.SecondHandActivity;
import com.dep.biguo.mvp.ui.activity.StudyToolActivity;
import com.dep.biguo.mvp.ui.activity.FarmAssistActivity;
import com.dep.biguo.mvp.ui.activity.ShopCartActivity;
import com.dep.biguo.mvp.ui.activity.AddressListActivity;
import com.dep.biguo.utils.MainAppUtils;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;

import java.util.ArrayList;
import java.util.List;

public class MallHomeFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = "MallHomeFragment";
    
    private MallHomeFragmentBinding binding;
    private MallProductAdapter studyPackageAdapter;
    private MallProductAdapter booksAdapter;

    public static MallHomeFragment newInstance() {
        Log.d(TAG, "newInstance() called");
        return new MallHomeFragment();
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        Log.d(TAG, "setupFragmentComponent() called");
        // 不需要注入，这是一个简单的UI页面
    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "initView() called");
        binding = DataBindingUtil.inflate(inflater, R.layout.mall_home_fragment, container, false);
        if (binding == null) {
            Log.e(TAG, "binding is null! Layout inflation failed!");
            return null;
        }
        Log.d(TAG, "binding created successfully");
        binding.setOnClickListener(this);

        setTranslucentStatusBar();

        // 设置顶部渐变色区域paddingTop为状态栏高度
        LinearLayout topLayout = (LinearLayout) binding.getRoot().findViewById(R.id.mallHomeTopLayout);
        if (topLayout != null) {
            int statusBarHeight = getStatusBarHeight();
            topLayout.setPadding(
                topLayout.getPaddingLeft(),
                statusBarHeight,
                topLayout.getPaddingRight(),
                topLayout.getPaddingBottom()
            );
        }

        return binding.getRoot();
    }

    private void setTranslucentStatusBar() {
        if (getActivity() == null) return;
        Window window = getActivity().getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            );
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.TRANSPARENT);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }
    }

    private int getStatusBarHeight() {
        int result = 0;
        int resourceId = getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            result = getResources().getDimensionPixelSize(resourceId);
        }
        return result;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        Log.d(TAG, "initData() called");
        loadMockData();
    }

    private void loadMockData() {
        Log.d(TAG, "loadMockData() called");
        
        try {
            // 模拟学习套装数据
            List<ShopBean> studyPackages = new ArrayList<>();
            for (int i = 0; i < 4; i++) {
                ShopBean bean = new ShopBean();
                bean.setId(i + 1);
                bean.setName("笔果AI学习机+教材 套装");
                bean.setPrice("3888.0");
                bean.setPreferential_price("4000.0");
                studyPackages.add(bean);
            }
            Log.d(TAG, "Created " + studyPackages.size() + " study packages");
            
            // 检查RecyclerView是否存在
            if (binding.rvStudyPackages == null) {
                Log.e(TAG, "rvStudyPackages is null!");
                return;
            }
            
            // 重新创建适配器并设置数据
            studyPackageAdapter = new MallProductAdapter(studyPackages);
            Log.d(TAG, "Study package adapter created with " + studyPackageAdapter.getItemCount() + " items");
            
            GridLayoutManager layoutManager1 = new GridLayoutManager(getContext(), 2);
            binding.rvStudyPackages.setLayoutManager(layoutManager1);
            binding.rvStudyPackages.setAdapter(studyPackageAdapter);
            Log.d(TAG, "Study packages RecyclerView setup complete");

            // 模拟书籍教材数据
            List<ShopBean> books = new ArrayList<>();
            for (int i = 0; i < 4; i++) {
                ShopBean bean = new ShopBean();
                bean.setId(i + 10);
                bean.setName("2025春季考试押密 英语");
                bean.setPrice("29.9");
                bean.setPreferential_price("35.0");
                books.add(bean);
            }
            Log.d(TAG, "Created " + books.size() + " books");
            
            // 检查RecyclerView是否存在
            if (binding.rvBooks == null) {
                Log.e(TAG, "rvBooks is null!");
                return;
            }
            
            // 重新创建适配器并设置数据
            booksAdapter = new MallProductAdapter(books);
            Log.d(TAG, "Books adapter created with " + booksAdapter.getItemCount() + " items");
            
            GridLayoutManager layoutManager2 = new GridLayoutManager(getContext(), 2);
            binding.rvBooks.setLayoutManager(layoutManager2);
            binding.rvBooks.setAdapter(booksAdapter);
            Log.d(TAG, "Books RecyclerView setup complete");
            
            // 设置点击事件
            studyPackageAdapter.setOnItemClickListener((adapter, view, position) -> {
                Log.d(TAG, "Study package clicked at position: " + position);
                ShopBean item = studyPackageAdapter.getItem(position);
                if (item != null) {
                    ShopDetailActivity.Start(getContext(), item.getId());
                }
            });

            booksAdapter.setOnItemClickListener((adapter, view, position) -> {
                Log.d(TAG, "Book clicked at position: " + position);
                ShopBean item = booksAdapter.getItem(position);
                if (item != null) {
                    ShopDetailActivity.Start(getContext(), item.getId());
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "Error in loadMockData(): " + e.getMessage(), e);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        
        if (id == R.id.layoutStudyTools) {
            // 学习工具
            ArmsUtils.startActivity(StudyToolActivity.class);
        } else if (id == R.id.layoutUsedBooks) {
            // 二手教材 - 跳转到二手教材页面
            ArmsUtils.startActivity(SecondHandActivity.class);
        } else if (id == R.id.layoutStudyArea) {
            // 助农专区
            FarmAssistActivity.start(getContext());
        } else if (id == R.id.layoutAddress) {
            // 收货地址
            if (!MainAppUtils.checkLogin(getContext())) return;
            // 跳转到收货地址管理页面
            ArmsUtils.startActivity(AddressListActivity.class);
        } else if (id == R.id.tvStudyPackageMore) {
            // 学习套装更多
            TextBooksActivity.start(getContext(), 1, "学习套装");
        } else if (id == R.id.tvBooksMore) {
            // 书籍教材更多
            TextBooksActivity.start(getContext(), 2, "书籍教材");
        } else if (id == R.id.ivSearchIcon) {
            // 搜索
            // TODO: 实现搜索功能
        }
    }

    @Override
    public void setData(@Nullable Object data) {
        // 可以接收外部传入的数据
    }
}
