package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CityBean;
import com.dep.biguo.bean.ProfessionBean;
import com.dep.biguo.bean.SchoolBean;
import com.dep.biguo.mvp.contract.CkProfessionSchoolContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class CkProfessionSchoolPresenter extends BasePresenter<CkProfessionSchoolContract.Model, CkProfessionSchoolContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public CkProfessionSchoolPresenter(CkProfessionSchoolContract.Model model, CkProfessionSchoolContract.View rootView) {
        super(model, rootView);
    }
    public void getSchoolList(int province, int layer_id) {
        mModel.getSchoolList(province, 1, layer_id)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<SchoolBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<SchoolBean>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showEmptyView();
                                mRootView.getSchoolListSuccess(new ArrayList<>());

                            }else {
                                mRootView.showSuccessView(false);
                                mRootView.getSchoolListSuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    /**获取专业
     *
     */
    public void getProfession(int school_id, int layer_id) {
        mModel.getProfession(school_id, 1, layer_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<ProfessionBean>(mErrorHandler) {
                    @Override
                    public void onNext(ProfessionBean response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showSuccessView(false);
                                mRootView.getProfessionSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView(false);
                                List<ProfessionBean> professionList = new ArrayList<>();
                                for(ProfessionBean profession : response.getData()){
                                    if(profession.getLayer() == layer_id) {
                                        professionList.add(profession);
                                    }
                                }
                                mRootView.getProfessionSuccess(professionList);
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(null);
                    }
                });
    }

    /**绑定省份、学校、专业
     *
     */
    public void bindProfession(CityBean.City city, SchoolBean schoolBean, int layer_id, ProfessionBean professionBean) {
        if (UserCache.getUserCache() == null) {
            mRootView.bindSuccess(schoolBean,professionBean);
            return;
        }
        String longitude = UserCache.getLongitude();
        String latitude = UserCache.getLatitude();
        mModel.bindProfession(city.getProvince_id(), 1, city.getCity_id(), layer_id, schoolBean.getId(), professionBean.getId(), professionBean.getAdult_professions_id(), longitude, latitude)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.bindSuccess(schoolBean,professionBean);
                        }
                    }
                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
