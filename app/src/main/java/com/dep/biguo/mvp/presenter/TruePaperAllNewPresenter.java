package com.dep.biguo.mvp.presenter;

import android.app.Activity;
import android.app.Application;
import android.text.TextUtils;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.AdBean;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.TaskBean;
import com.dep.biguo.bean.TruePaperAllBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.bean.jsz.PracticeAdsBean;
import com.dep.biguo.mvp.contract.TruePaperAllContract;
import com.dep.biguo.mvp.contract.TruePaperAllNewContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.wxapi.WxMinApplication;
import com.google.gson.Gson;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class TruePaperAllNewPresenter extends BasePresenter<TruePaperAllNewContract.Model, TruePaperAllNewContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public TruePaperAllNewPresenter(TruePaperAllNewContract.Model model, TruePaperAllNewContract.View rootView) {
        super(model, rootView);
    }

    /**获取课程列表
     *
     */
    public void getCourseList(){
        int profession_id = UserCache.getProfession().getId();
        mModel.getCourseList(profession_id)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable ->mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoading())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<TruePaperAllBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<TruePaperAllBean>> s) {
                        if (s.isSuccess()) {
                            if(AppUtil.isEmpty(s.getData())){
                                mRootView.showEmptyView();
                                mRootView.getCourseListSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getCourseListSuccess(s.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void getAppTerrible(int province_id) {
        mModel.getAppTerrible(province_id, "give_good_review")
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<AdBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<AdBean> response) {
                        if (response.isSuccess()) {
                            startWechat(response.getData());
                            mRootView.startWxSuccess();
                        }
                    }
                });
    }

    public void finishTask(String type){
        mModel.finishTask(type)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            UserCache.cacheShowGoodApp(true);
                            mRootView.showMessage("请查收积分奖励");
                        }
                    }
                });
    }

    /**点击了微信群或微信客服
     * @param bean
     */
    public void startWechat(AdBean bean){
        Activity activity = AppManager.getAppManager().getTopActivity();
        if(TextUtils.isEmpty(bean.getXcx_path())){
            if(bean.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(activity)) return;

            HtmlActivity.start(activity, bean.getTarget_url());
        }else {
            if(bean.getNeed_login() == StartFinal.YES && !MainAppUtils.checkLogin(activity)) return;

            WxMinApplication.StartWechat(activity, bean.getXcx_path(), bean.getTarget_url());
        }
    }

    public void getAds() {
        mModel.getAds("", 0, "real_paper")
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PracticeAdsBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<PracticeAdsBean> response) {
                        if (response.isSuccess()) {
                            mRootView.getAdsSuccess(response.getData().getReal_paper());
                        }
                    }
                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
