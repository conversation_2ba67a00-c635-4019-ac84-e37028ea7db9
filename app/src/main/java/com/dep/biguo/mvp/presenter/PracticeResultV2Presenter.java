package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.AiScoreBean;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.DayCardV3Bean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.mvp.contract.PracticeResultV2Contract;
import com.dep.biguo.mvp.ui.adapter.practice.PracticeCardAdapter;
import com.dep.biguo.utils.PracticeHelper;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.mmkv.UserCache;
import com.biguo.utils.widget.LoadingDialog;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.simple.eventbus.EventBus;

import java.util.HashMap;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;

@ActivityScope
public class PracticeResultV2Presenter extends BasePresenter<PracticeResultV2Contract.Model, PracticeResultV2Contract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject PracticeCardAdapter mPracticeResultAdapter;


    @Inject
    public PracticeResultV2Presenter(PracticeResultV2Contract.Model model, PracticeResultV2Contract.View rootView) {
        super(model, rootView);
    }

    public void getVipOrYamiOpenStatus(String code, int mainType) {
        if(UserCache.getUserCache() == null) return;
        if(mainType != PracticeHelper.PRACTICE_COURSE
                && mainType != PracticeHelper.PRACTICE_TRUE
                && mainType != PracticeHelper.PRACTICE_VIP
                && mainType != PracticeHelper.PRACTICE_CHAPTER
                && mainType != PracticeHelper.PRACTICE_TYPE_HIGH
                && mainType != PracticeHelper.PRACTICE_DAYCARD){
            //不是免费题库、不是真题、不是VIP题库，不是章节，不是高频考点，不是每日打卡，则不请求
            mRootView.showOpenQuestion(false, false);
            return;
        }
        //不是自考也不请求
        if(!Constant.ZK.equals(UserCache.getAppType())) return;

        mModel.getVipOrYamiOpenStatus(code, mainType)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        mRootView.hideLoading();
                        try {
                            if (response.isSuccess()) {
                                String json = GsonUtils.toJson(response.getData());
                                Map<String, String> map = GsonUtils.fromJson(json, new TypeToken<HashMap<String, String>>(){}.getType());
                                if(map.containsKey("type")){
                                    mRootView.showOpenQuestion(map.containsValue(StartFinal.VIP), map.containsValue(StartFinal.YAMI));
                                }
                            }
                        }catch (Exception e){
                            LogUtil.e("dddd", response);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void signIn(){
        mModel.signIn()
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<DayCardV3Bean.Prize>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<DayCardV3Bean.Prize> response) {
                        mRootView.hideLoading();
                        if (response.isSuccess()) {
                            EventBus.getDefault().post("", EventBusTags.DAYCARD);
                            mRootView.signSuccess(response.getData());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void practiceCommit() {
        if(UserCache.getUserCache() == null) return;

        Map<String, Object> params = new HashMap<>();
        params.put("is_daily_practice", mRootView.getIsDayCard());
        params.put("code", mRootView.getCode());
        params.put("mainType", mRootView.getMainType());
        params.put("topic_type", mRootView.getTopicType());
        params.put("status", mRootView.getIsDoError());
        params.put("answer_count", mRootView.getAnswerCount());
        params.put("undone", mRootView.getUndoneCount());
        params.put("probability", mRootView.getProbability());

        mModel.practiceCommit(params)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        mRootView.hideLoading();
                        if (response.isSuccess()) {
                            //打卡成功，积分自动加2
                            if(mRootView.getIsDayCard() == StartFinal.YES) {
                                UserCache.getUserCache().setIntegral(UserCache.getUserCache().getIntegral() + 2);
                            }
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void getAiScore() {
        if(UserCache.getUserCache() == null) {
            mRootView.practiceCommitSuccess(null);
            return;
        };

        mModel.getAiScore(mRootView.getCode(), mRootView.getMainType())
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<AiScoreBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<AiScoreBean> response) {
                        mRootView.hideLoading();
                        if (response.isSuccess()) {
                            mRootView.practiceCommitSuccess(response.getData());
                        }else {
                            mRootView.practiceCommitSuccess(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.practiceCommitSuccess(null);
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
