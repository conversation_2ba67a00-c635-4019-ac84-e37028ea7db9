package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.TruePaperAllBean;
import com.dep.biguo.bean.TruePaperNewItemBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.TruePaperItemNewContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.mvp.ui.adapter.TruePaperAdapter;
import com.dep.biguo.utils.database.util.RealQuery;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.jess.arms.di.scope.FragmentScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@FragmentScope
public class TruePaperItemNewPresenter extends BasePresenter<TruePaperItemNewContract.Model, TruePaperItemNewContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private PayResultListener mResultPayListener;

    @Inject
    public TruePaperItemNewPresenter(TruePaperItemNewContract.Model model, TruePaperItemNewContract.View rootView) {
        super(model, rootView);
        mResultPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                mRootView.paySuccess();
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {

            }
        };
    }
    public void getTruePaper(int courseId, String code, int page) {
        mModel.getTruePaper(courseId, page)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<TruePaperNewItemBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<TruePaperNewItemBean>> response) {
                        if (response.isSuccess()) {
                            if (AppUtil.isEmpty(response.getData())) {
                                mRootView.showEmptyView();
                                mRootView.getTruePaperSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getTruePaperSuccess(response.getData());

                                //.insertTruePaper(code, response.getData());
                            }
                        }else {
                            mRootView.getTruePaperFail();
                            mRootView.showErrorView(new Throwable());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        //super.onError(t);
                        RealQuery.queryTruePaper(code, truePapers -> {
                            if(!AppUtil.isEmpty(truePapers)) {
                                BaseResponse<List<TruePaperNewItemBean>> response = new BaseResponse<>();
                                response.setData(page == 1 ? truePapers : new ArrayList<>());
                                response.setResult_code("1");
                                onNext(response);
                            }else {
                                mRootView.getTruePaperFail();
                                mRootView.showErrorView(t);
                            }
                        });
                    }
                });
    }
    /**支付
     * @param payType 支付方式，参考{@link PayUtils#PAY_TYPE_WEXIN}
     */
    public void payOrder(Map<String,Object> paramsMap, String payType) {
        paramsMap.put(PayParamsBean.PAY_TYPE,payType);
        mModel.paySingleOrder(paramsMap)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<PayBean> s) {
                        if (s.isSuccess()) {
                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                WXPayBean wxPayBean = GsonUtils.fromJson(GsonUtils.toJson(s.getData().getPay()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().getPay().toString());
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_COIN.equals(payType) || PayUtils.PAY_TYPE_INTEGRAL.equals(payType)){
                                mRootView.paySuccess();
                            }
                        }
                    }
                });
    }

    public void shareUnlock(int exams_real_paper_id) {
        mModel.shareUnlock(exams_real_paper_id, 0)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse result) {
                        if (result.isSuccess()) {
                            mRootView.shareUnlockSuccess(exams_real_paper_id);
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
