package com.dep.biguo.mvp.presenter;

import android.app.Application;
import android.text.TextUtils;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.bean.AlipayAccountBean;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.ProvinceBean;
import com.dep.biguo.bean.RuleBean;
import com.dep.biguo.bean.ScholarshipInputBean;
import com.dep.biguo.bean.ScholarshipRankBean;
import com.dep.biguo.bean.SchoolDetailBean;
import com.dep.biguo.mvp.contract.ScholarshipRankContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class ScholarshipRankPresenter extends BasePresenter<ScholarshipRankContract.Model, ScholarshipRankContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public ScholarshipRankPresenter(ScholarshipRankContract.Model model, ScholarshipRankContract.View rootView) {
        super(model, rootView);
    }

    public void getScholarshipInput() {
        mModel.getScholarshipInput()
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoading())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<ScholarshipInputBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<ScholarshipInputBean> s) {
                        if (s.isSuccess()) {
                            mRootView.showSuccessView();
                            mRootView.getScholarshipInputSuccess(s.getData());
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void getScholarshipRank(String code) {
        mModel.getScholarshipRank(code)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoading())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoading())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<ScholarshipRankBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<ScholarshipRankBean> s) {
                        if (s.isSuccess()) {
                            mRootView.showSuccessView();
                            mRootView.getScholarshipRankSuccess(s.getData());
                        }else {
                            mRootView.showMessage(s.getResult_info());
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void getProvince() {
        mModel.getProvince()
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<ProvinceBean>(mErrorHandler) {
                    @Override
                    public void onNext(ProvinceBean response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showMessage("暂无省份");
                            }else {
                                mRootView.getProvinceSuccess(response.getData());
                            }
                        }
                    }
                });
    }

    public void getRule() {
        mModel.getRule("scholarship_score_rule")
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<RuleBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<RuleBean> response) {
                        if (response.isSuccess()) {
                            mRootView.getRuleSuccess(response.getData().getRules());
                        }
                    }
                });
    }


    public void commitScholarshipScore(String code, ProvinceBean province, String score, String number, String password, AlipayAccountBean alipay) {
        if(province == null){
            mRootView.showMessage("请选择省份");
            return;
        }

        if(TextUtils.isEmpty(score)){
            mRootView.showMessage("请输入成绩");
            return;
        }

        if(TextUtils.isEmpty(number)){
            mRootView.showMessage("请输入准考证号");
            return;
        }

        if(alipay == null){
            mRootView.showMessage("请绑定支付宝账号");
            return;
        }

        mModel.commitScholarshipScore(code, province.getId(), Float.parseFloat(score), number, password)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.commitScholarshipScoreSuccess();
                        }
                    }
                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
