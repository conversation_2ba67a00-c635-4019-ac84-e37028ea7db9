package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.InviteToIntroduceBean;
import com.dep.biguo.bean.RewardCollectBean;
import com.dep.biguo.mvp.contract.InviteToIntroduceContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class InviteToIntroducePresenter extends BasePresenter<InviteToIntroduceContract.Model, InviteToIntroduceContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public InviteToIntroducePresenter(InviteToIntroduceContract.Model model, InviteToIntroduceContract.View rootView) {
        super(model, rootView);
    }

    public void getInviteToIntroduce(){
        mModel.getInviteToIntroduce()
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<InviteToIntroduceBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<InviteToIntroduceBean> response) {
                        if (response.isSuccess()) {
                            mRootView.showSuccessView();
                            mRootView.getInviteToIntroduceSuccess(response.getData());
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
