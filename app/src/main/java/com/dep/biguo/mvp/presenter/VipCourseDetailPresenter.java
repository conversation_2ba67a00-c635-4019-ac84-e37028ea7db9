package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;
import android.os.Handler;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.VipCourseDetailBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.VipCourseDetailContract;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;
import com.trello.rxlifecycle2.android.ActivityEvent;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;


/**
 * ================================================
 * Description:
 * <p>
 * ================================================
 */
@ActivityScope
public class VipCourseDetailPresenter extends BasePresenter<VipCourseDetailContract.Model, VipCourseDetailContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;
    private PayResultListener mPayListener;

    @Inject
    public VipCourseDetailPresenter(VipCourseDetailContract.Model model, VipCourseDetailContract.View rootView) {
        super(model, rootView);
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    void onCreate() {
        mPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                int mClassRoomId = mRootView.getClassRoomId();
                new Handler().postDelayed(() -> getVIPCourseDetail(mClassRoomId), 500);
                //JuliangHelper.reportPay("vip", String.valueOf(mClassRoomId), "app", 36);
            }

            @Override
            public void onPayError() {
            }

            @Override
            public void onPayCancel() {
            }
        };
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
    }

    public void getVIPCourseDetail(int classroom_id) {
        mModel.getVIPCourseDetail(classroom_id)
                .subscribeOn(Schedulers.io())

                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, ActivityEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<VipCourseDetailBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<VipCourseDetailBean> s) {
                        if (s.isSuccess()) {
                            if (s.getData() != null)
                                mRootView.fillData(s.getData());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                    }
                });
    }

    public void getDiscount(int users_id) {
        mModel.getDiscount(users_id)
                .subscribeOn(Schedulers.io())

                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, ActivityEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<DiscountBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<DiscountBean>> s) {
                        if (s.isSuccess()) {
//                            int id = 0;
                            List<DiscountBean> beans = new ArrayList<>();
                            for (DiscountBean bean : s.getData()) {
                                //有可用优惠券
                                if (bean.getStatus() == 1) {
//                                    id = bean.getId();
                                    beans.add(bean);
                                }
                            }
                            mRootView.setDiscountId(beans);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                    }
                });
    }

    public void buyByFruit(int classroom_id, int coupons_id) {
        mModel.payForFruitVipcourse(classroom_id, coupons_id)
                .subscribeOn(Schedulers.io())

                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, ActivityEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if (s.isSuccess()) {

                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                    }
                });
    }

    public void buyByWechat(int classroom_id, int coupons_id) {
        mModel.payForWechatVipcourse(classroom_id, coupons_id)
                .subscribeOn(Schedulers.io())

                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, ActivityEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<WXPayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<WXPayBean> response) {
                        if (response.isSuccess()) {
                            new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WX, response.getData(), "");
                            PayListenerUtils.getInstance().setListener(mPayListener);
                        } else if (response.isFail()) {
                            mRootView.showMessage(response.getResult_info());
                        }

                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                    }
                });
    }

    public void buyByAlipay(int classroom_id, int coupons_id) {
        mModel.payForAlipayVipcourse(classroom_id, coupons_id)
                .subscribeOn(Schedulers.io())

                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, ActivityEvent.STOP))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<String>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<String> response) {
                        if (response.isSuccess()) {
                            new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALI, null, response.getData());
                            PayListenerUtils.getInstance().setListener(mPayListener);
                        } else if (response.isFail()) {
                            mRootView.showMessage(response.getResult_info());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                    }
                });
    }

//    public void buyByPart(int classroom_id, int coupons_id) {
//        mModel.payForAlipayVipcourse(classroom_id, coupons_id)
//                .subscribeOn(Schedulers.io())
//
//                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
//                .subscribeOn(AndroidSchedulers.mainThread())
//                .observeOn(AndroidSchedulers.mainThread())
//                .doFinally(() -> mRootView.hideLoadingDialog())
//                .compose(RxLifecycleUtils.bindUntilEvent(mRootView, ActivityEvent.STOP))
//                .subscribe(new ErrorHandleSubscriber<BaseResponse<String>>(mErrorHandler) {
//                    @Override
//                    public void onNext(BaseResponse<String> s) {
//                        if (s.isSuccess()) {
//
//                        }
//                    }
//
//                    @Override
//                    public void onError(Throwable t) {
//                        super.onError(t);
//                    }
//                });
//    }
}
