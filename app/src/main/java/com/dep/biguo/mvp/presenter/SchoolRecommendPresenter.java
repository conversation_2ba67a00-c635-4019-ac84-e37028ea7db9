package com.dep.biguo.mvp.presenter;

import android.app.Application;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.SchoolDataBean;
import com.dep.biguo.mvp.contract.SchoolRecommendContract;
import com.dep.biguo.mvp.ui.adapter.HomeSchoolAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;


@ActivityScope
public class SchoolRecommendPresenter extends BasePresenter<SchoolRecommendContract.Model, SchoolRecommendContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;
    @Inject
    HomeSchoolAdapter mHomeSchoolAdapter;

    private int mPage = 1;
    private static final int INIT = 0;
    private static final int REFRESH = 1;
    private static final int LOAD = 2;

    @Inject
    public SchoolRecommendPresenter(SchoolRecommendContract.Model model, SchoolRecommendContract.View rootView) {
        super(model, rootView);
    }


    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    public void initRefresh(){
        getData(INIT,1);
    }

    public void refresh(){
        getData(REFRESH,1);
    }

    public void loadMore(){
        getData(LOAD,mPage+1);
    }

    private void getData(int action, int page) {
        int province_id = 0;
        if (UserCache.getProvince() != null)
            province_id = UserCache.getProvince().getId();

        mModel.getSchoolList(province_id, page,10)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> {if (action == INIT) mRootView.showLoading();})
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> {if (action == INIT) mRootView.hideLoading();})
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<SchoolDataBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<SchoolDataBean>> s) {
                        if (s.isSuccess()) {
                            if (action == INIT || action == REFRESH) {
                                mHomeSchoolAdapter.setNewData(s.getData());
                                mRootView.finishRefresh();
                                if (AppUtil.isEmpty(s.getData()))
                                    mRootView.showEmptyView();
                                mPage = 1;//刷新成功，将页数设置为1
                            } else {
                                mHomeSchoolAdapter.addData(s.getData());
                                mHomeSchoolAdapter.loadMoreComplete();
                                mPage += 1;//加载更多成功，将请求页数自加1
                            }

                            if (AppUtil.isEmpty(s.getData()))
                                mHomeSchoolAdapter.loadMoreEnd();
                        }else {
                            refreshOrLoadAnimation();
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        refreshOrLoadAnimation();
                        if (AppUtil.isEmpty(mHomeSchoolAdapter.getData()))
                            mRootView.showErrorView(t);
                    }

                    //取消刷新或加载的动画
                    public void refreshOrLoadAnimation(){
                        switch (action){
                            case REFRESH:mRootView.finishRefresh();break;
                            case LOAD:mHomeSchoolAdapter.loadMoreFail();break;
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
    }
}
