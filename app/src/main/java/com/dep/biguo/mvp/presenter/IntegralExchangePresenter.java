package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;
import android.content.Intent;

import com.dep.biguo.R;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.IntegralExchangeBean;
import com.dep.biguo.bean.IntegralExchangeListBean;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.mvp.contract.IntegralExchangeContract;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.mmkv.UserCache;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;

@ActivityScope
public class IntegralExchangePresenter extends BasePresenter<IntegralExchangeContract.Model, IntegralExchangeContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private String goodsType;
    private IntegralExchangeBean productBean;

    @Inject
    public IntegralExchangePresenter(IntegralExchangeContract.Model model, IntegralExchangeContract.View rootView) {
        super(model, rootView);
    }

    public void init(Intent intent){
        goodsType = intent.getStringExtra(StartFinal.GOODS_TYPE);
    }

    public IntegralExchangeBean getProductBean() {
        return productBean;
    }

    public String getGoodsType() {
        return goodsType;
    }

    public String getTitle(){
        return StartFinal.VIP.equals(goodsType) ? "VIP题库免费兑换" : "考前押密免费兑换";
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    private void onCreate(){
        getIntegralProduct();
    }

    public void getIntegralProduct(){
        mModel.getIntegralProduct(goodsType)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<IntegralExchangeBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<IntegralExchangeBean> s) {
                        if (s.isSuccess()) {
                            productBean = s.getData();
                            mRootView.setProduct(s.getData());
                        }
                    }
                });
    }

    public void getIntegralExchangeList(){
        mModel.getIntegralExchangeList(goodsType)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<IntegralExchangeListBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<IntegralExchangeListBean>> s) {
                        if (s.isSuccess()) {
                            if(s.getData().size() > 0) {
                                mRootView.showIntegralExchangeDialog(s.getData());
                            }
                        }
                    }
                });
    }

    public void integralExchange(String code){
        mModel.integralExchange(goodsType, code)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<IntegralExchangeBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<IntegralExchangeBean> s) {
                        if (s.isSuccess()) {
                            UserBean userBean = UserCache.getUserCache();
                            userBean.setIntegral(Float.parseFloat(productBean.getIntegral()) - Float.parseFloat(productBean.getExchange_price())+"");
                            UserCache.cacheUser(userBean);
                            mRootView.showMessage("兑换成功");
                            getIntegralProduct();
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
