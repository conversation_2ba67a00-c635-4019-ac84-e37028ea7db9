package com.dep.biguo.mvp.ui.adapter;

import android.graphics.Color;
import android.graphics.Paint;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.utils.image.ImageLoader;
import com.dep.biguo.utils.PriceSpannableUtil;

import java.util.List;

/**
 * 商城商品适配器
 */
public class MallProductAdapter extends BaseQuickAdapter<ShopBean, BaseViewHolder> {

    private static final String TAG = "MallProductAdapter";

    public MallProductAdapter(@Nullable List<ShopBean> data) {
        super(R.layout.item_mall_product, data);
        Log.d(TAG, "MallProductAdapter created with " + (data != null ? data.size() : 0) + " items");
    }

    @Override
    protected void convert(BaseViewHolder helper, ShopBean item) {
        Log.d(TAG, "convert() called for position: " + helper.getAdapterPosition() + ", item: " + (item != null ? item.getName() : "null"));
        
        if (item == null) {
            Log.e(TAG, "Item is null at position: " + helper.getAdapterPosition());
            return;
        }
        
        try {
            // 商品图片
            ImageView ivProductImage = helper.getView(R.id.ivProductImage);
            if (item.getImg() != null && !item.getImg().isEmpty()) {
                Log.d(TAG, "Loading image: " + item.getImg());
                ImageLoader.loadImage(ivProductImage, item.getImg());
            } else {
                Log.d(TAG, "No image, using placeholder");
                ivProductImage.setImageResource(R.drawable.bg_round_8_gray);
            }

            // 商品标题
            Log.d(TAG, "Setting title: " + item.getName());
            helper.setText(R.id.tvProductTitle, item.getName());

            // 副标题 (出版社 | 专业分类)
            String subTitle = "";
            if (item.getPublisher() != null && !item.getPublisher().isEmpty()) {
                subTitle = item.getPublisher();
            }
            if (item.getCategory() != null && !item.getCategory().isEmpty()) {
                if (!subTitle.isEmpty()) {
                    subTitle += " | ";
                }
                subTitle += item.getCategory();
            }
            if (subTitle.isEmpty()) {
                subTitle = "专升本教材";
            }
            helper.setText(R.id.tvSubTitle, subTitle);

            // 使用SpannableString优化价格显示
            TextView tvPrice = helper.getView(R.id.tvPrice);
            SpannableString priceSpannable = PriceSpannableUtil.createPriceSpannable(
                mContext,
                item.getPrice(),
                item.getPreferential_price()
            );
            tvPrice.setText(priceSpannable);
            Log.d(TAG, "Setting price with SpannableString: " + priceSpannable.toString());

            // 成色标签
            TextView tvConditionTag = helper.getView(R.id.tvConditionTag);
            if (item.getCondition() != null && !item.getCondition().isEmpty()) {
                tvConditionTag.setVisibility(View.VISIBLE);
                tvConditionTag.setText(item.getCondition());
            } else {
                tvConditionTag.setVisibility(View.GONE);
            }

            // 急售标签
            TextView tvUrgentTag = helper.getView(R.id.tvUrgentTag);
            if (item.isUrgent()) {
                tvUrgentTag.setVisibility(View.VISIBLE);
            } else {
                tvUrgentTag.setVisibility(View.GONE);
            }

            // 位置信息
            TextView tvLocation = helper.getView(R.id.tvLocation);
            if (item.getLocation() != null && !item.getLocation().isEmpty()) {
                tvLocation.setText(item.getLocation());
                tvLocation.setVisibility(View.VISIBLE);
            } else {
                tvLocation.setVisibility(View.GONE);
            }

            // 发布时间
            TextView tvPublishTime = helper.getView(R.id.tvPublishTime);
            if (item.getPublishTime() != null && !item.getPublishTime().isEmpty()) {
                tvPublishTime.setText(item.getPublishTime());
                tvPublishTime.setVisibility(View.VISIBLE);
            } else {
                tvPublishTime.setVisibility(View.GONE);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error in convert() at position " + helper.getAdapterPosition() + ": " + e.getMessage(), e);
        }
    }
} 