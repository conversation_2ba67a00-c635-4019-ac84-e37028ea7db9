package com.dep.biguo.mvp.presenter;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;
import android.content.Intent;
import android.text.TextUtils;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.GroupBean;
import com.dep.biguo.bean.PayBean;
import com.dep.biguo.bean.PayParamsBean;
import com.dep.biguo.bean.SkillGroupGoodInfoBean;
import com.dep.biguo.bean.WXPayBean;
import com.dep.biguo.mvp.contract.SkillGroupGoodsContract;
import com.dep.biguo.mvp.ui.activity.StudyActivity;
import com.dep.biguo.mvp.ui.activity.VideoTypeListActivity;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.pay.PayListenerUtils;
import com.dep.biguo.utils.pay.PayResultListener;
import com.dep.biguo.utils.pay.PayUtils;
import com.biguo.utils.widget.LoadingDialog;
import com.google.gson.Gson;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;

@ActivityScope
public class SkillGroupGoodsPresenter extends BasePresenter<SkillGroupGoodsContract.Model, SkillGroupGoodsContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;


    private PayResultListener mResultPayListener;//支付的监听接口

    private List<DiscountBean> discountList;//优惠券列表
    private String activityName;//上一个页面的类名称，用于判断是否需要弹出挽留弹窗
    private String goodsType;//标识当前页面显示哪种类型的题库
    private int source_type;//标识当前页面显示哪种类型的题库
    private int product_id;//课程代码
    private int skill_id;//技能证ID
    private boolean isBuy;//是否已购买
    private int rid;//发起拼团者的用户ID，直接拼成时需要携带上
    private String payType;//挽回弹窗弹出并直接拼成时，用户选择的支付方式
    private GroupBean joinGroup;//挽回弹窗弹出并直接拼成时，为用户选择的团
    private SkillGroupGoodInfoBean infoBean;

    private boolean isLaunchGroup;//判断是否是发起拼团
    private int orderId;//正在支付的订单ID，取消支付或支付成功后将置为0

    @Inject
    public SkillGroupGoodsPresenter(SkillGroupGoodsContract.Model model, SkillGroupGoodsContract.View rootView) {
        super(model, rootView);
    }

    public void init(Intent intent){
        activityName = intent.getStringExtra(StartFinal.ACTIVITY_NAME);
        goodsType = intent.getStringExtra(StartFinal.GOODS_TYPE);
        skill_id = intent.getIntExtra(StartFinal.SKILL_ID, 0);
        source_type = intent.getIntExtra(StartFinal.TYPE, 0);
        product_id = intent.getIntExtra(StartFinal.PRODUCT_ID, 0);
        isBuy = intent.getBooleanExtra(StartFinal.IS_BUY, false);
        //只有点击分享链接进入APP时，才会传递
        rid = intent.getIntExtra(StartFinal.RID, 0);
    }

    public String getActivityName() {
        return activityName;
    }

    public String getGoodsType() {
        return goodsType;
    }

    public int getSource_type() {
        return source_type;
    }

    public int getProduct_id() {
        return product_id;
    }

    public boolean isBuy() {
        if(infoBean == null){
            return isBuy;
        }else {
            return infoBean.getIs_pay() == StartFinal.YES;
        }
    }

    public int getSkill_id() {
        return skill_id;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public boolean isLaunchGroup() {
        return isLaunchGroup;
    }

    public void setLaunchGroup(boolean launchGroup) {
        isLaunchGroup = launchGroup;
    }

    public void setInfoBean(SkillGroupGoodInfoBean infoBean) {
        this.infoBean = infoBean;
    }

    public String getCourseName() {
        return infoBean == null ? "" : infoBean.getName();
    }

    public String getPayType() {
        return TextUtils.isEmpty(payType) ? PayUtils.PAY_TYPE_ALIPAY : payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public GroupBean getJoinGroup() {
        return joinGroup;
    }

    public void setJoinGroup(GroupBean joinGroup) {
        this.joinGroup = joinGroup;
    }

    public SkillGroupGoodInfoBean getInfoBean() {
        return infoBean;
    }

    public boolean isShowAgainPayDialog() {
        return !isBuy() && (VideoTypeListActivity.class.getName().equals(activityName)
                || StudyActivity.class.getName().equals(activityName));
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    void onCreate() {
        mResultPayListener = new PayResultListener() {
            @Override
            public void onPaySuccess() {
                orderId = 0;
                mRootView.paySuccess(isLaunchGroup);
            }

            @Override
            public void onPayError() {

            }

            @Override
            public void onPayCancel() {
                orderId = 0;
                isLaunchGroup = false;
                mRootView.payCancel(orderId);
            }
        };
    }

    /**获取优惠券
     *
     */
    public void getDiscountCard(String price, String payPrice, String groupId, int rid, boolean isGroup){
        if(discountList != null) {
            mRootView.showPayDialog(discountList, price, payPrice, groupId, rid, isGroup);
            return;
        }

        String type = goodsType;
        mModel.getDiscountCard(isGroup ? 1 : 0, type, "use", -1)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<DiscountBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<List<DiscountBean>> s) {
                        if (s.isSuccess()) {
                            discountList = s.getData();
                            mRootView.showPayDialog(discountList, price, payPrice, groupId, rid, isGroup);
                        }
                    }

                    @Override
                    public void onError(@NonNull Throwable t) {
                        mRootView.showMessage("获取优惠券列表失败");
                        mRootView.showPayDialog(new ArrayList<>(), price, payPrice, groupId, rid, isGroup);
                    }
                });
    }

    /**支付
     * @param payType 支付方式，参考{@link PayUtils#PAY_TYPE_WEXIN}
     */
    public void payOrder(boolean isGroup, String groupId, int rid, String payType, DiscountBean selectedDiscountBean) {
        //创建需要传递的参数对象
        Map<String,Object> paramsMap = getParams(isGroup, payType, rid, groupId, selectedDiscountBean).getParamsMap();
        //根据是否拼团选择对应的接口
        Observable<BaseResponse<PayBean>> observable = isGroup ? mModel.payGroupOrder(paramsMap) : mModel.paySingleOrder(paramsMap);

        observable.subscribeOn(Schedulers.io())
                
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<PayBean>>(mErrorHandler) {
                    @Override
                    public void onNext(@NotNull BaseResponse<PayBean> s) {
                        if (s.isSuccess()) {
                            //保存订单ID
                            orderId = s.getData().getOrder_id();
                            //当调起的订单使用了优惠券，优惠券此时被占用，则需要将使用的优惠券从优惠券列表中移除
                            if(selectedDiscountBean != null) {
                                discountList.remove(selectedDiscountBean);
                            }
                            isLaunchGroup = isGroup && TextUtils.isEmpty(groupId);
                            //优惠额度大于等于支付价格，不需要调起支付界面，处理方式与果币、积分支付一致，
                            if(s.getData() != null && s.getData().getPay() instanceof Boolean){
                                mRootView.paySuccess(isLaunchGroup);
                                return;
                            }

                            if(PayUtils.PAY_TYPE_WEXIN.equals(payType)){
                                Gson gson = new Gson();
                                WXPayBean wxPayBean = gson.fromJson(gson.toJson(s.getData().getPay()),WXPayBean.class);
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_WEXIN, wxPayBean, "");
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }else if(PayUtils.PAY_TYPE_ALIPAY.equals(payType)){
                                new PayUtils().pay(mRootView.getActivity(), PayUtils.PAY_TYPE_ALIPAY, null, s.getData().getPay().toString());
                                PayListenerUtils.getInstance().setListener(mResultPayListener);

                            }
                        }else {
                            //被抢拼的团将会被锁定45秒，45秒内不能使用拼团ID创建新的订单
                            mRootView.showMessage(s.getResult_info());
                        }
                    }
                });
    }

    /**我要评论
     * @param assess
     */
    public void addAssess(String assess) {
        if(assess.length() < 6) {
            mRootView.showMessage("评论不能少于6个字");
            return;
        }

        mModel.addAssess(goodsType, skill_id, infoBean.getProduct_id() + "", assess, infoBean.getOrder_id()+"", infoBean.getSource_type(),infoBean.getCert_type())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.showMessage(response.getResult_info());
                            mRootView.commentSuccess();
                        }
                    }
                });
    }

    /**返回顶部标题
     * @return
     */
    public String getTitle(){
        return infoBean == null ? "" : infoBean.getName();
    }

    public String getGoodTypeName(){
        return StartFinal.SKILL_VIDEO.equals(goodsType) ? "职业技能" : "职场提升";
    }

    /**
     * @param isGroup
     * @param group_id
     * @return
     */
    private PayParamsBean getParams(boolean isGroup, String payType, int rid, String group_id, DiscountBean selectedDiscountBean) {
        PayParamsBean paramsBean = PayParamsBean.init();
        //paramsBean.put(PayParamsBean.CODE,courseCode);
        paramsBean.put(PayParamsBean.TYPE, goodsType);
        paramsBean.put(PayParamsBean.IS_GROUP, isGroup ? "1":"0");//1拼团，0单独购买
        paramsBean.put(PayParamsBean.GROUP_ID, group_id);//发起拼团传0，拼团传group_id
        paramsBean.put(PayParamsBean.R_ID, rid + "");
        paramsBean.put(PayParamsBean.PAY_TYPE, payType);
        paramsBean.put(PayParamsBean.COUPON_ID, selectedDiscountBean == null ? 0 : selectedDiscountBean.getId());
        paramsBean.put(PayParamsBean.SOURCE_TYPE, getSource_type());
        paramsBean.put(PayParamsBean.PRODUCT_ID, infoBean.getProduct_id());
        paramsBean.put(PayParamsBean.SKILL_ID, skill_id);

        return paramsBean;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
