package com.dep.biguo.mvp.ui.activity;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.dep.biguo.R;
import com.dep.biguo.databinding.PublishBookActivityBinding;

public class PublishBookActivity extends AppCompatActivity implements View.OnClickListener {

    private PublishBookActivityBinding binding;
    private String selectedCondition = "全新";
    private String selectedShipping = "自己填";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.publish_book_activity);
        binding.setOnClickListener(this);

        setStatusBarColor();
        initViews();
    }

    private void setStatusBarColor() {
        Window window = getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            );
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    private void initViews() {
        // 设置默认选中状态
        updateConditionSelection("全新");
        updateShippingSelection("自己填");
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        
        if (id == R.id.ivBack) {
            finish();
        } else if (id == R.id.btnPublish) {
            publishBook();
        } else if (id == R.id.layoutAddImage) {
            // TODO: 添加图片功能
            Toast.makeText(this, "添加图片功能", Toast.LENGTH_SHORT).show();
        } else if (id == R.id.tvConditionNew) {
            updateConditionSelection("全新");
        } else if (id == R.id.tvConditionAlmostNew) {
            updateConditionSelection("几乎全新");
        } else if (id == R.id.tvConditionLight) {
            updateConditionSelection("轻微使用痕迹");
        } else if (id == R.id.tvConditionObvious) {
            updateConditionSelection("明显使用痕迹");
        } else if (id == R.id.tvShippingSelf) {
            updateShippingSelection("自己填");
        } else if (id == R.id.tvShippingFree) {
            updateShippingSelection("包邮");
        } else if (id == R.id.tvShippingPickup) {
            updateShippingSelection("自提");
        }
    }

    private void updateConditionSelection(String condition) {
        selectedCondition = condition;
        
        // 重置所有按钮状态
        resetConditionButtons();
        
        // 设置选中状态
        switch (condition) {
            case "全新":
                binding.tvConditionNew.setBackgroundResource(R.drawable.bg_condition_selected);
                binding.tvConditionNew.setTextColor(getResources().getColor(R.color.white));
                break;
            case "几乎全新":
                binding.tvConditionAlmostNew.setBackgroundResource(R.drawable.bg_condition_selected);
                binding.tvConditionAlmostNew.setTextColor(getResources().getColor(R.color.white));
                break;
            case "轻微使用痕迹":
                binding.tvConditionLight.setBackgroundResource(R.drawable.bg_condition_selected);
                binding.tvConditionLight.setTextColor(getResources().getColor(R.color.white));
                break;
            case "明显使用痕迹":
                binding.tvConditionObvious.setBackgroundResource(R.drawable.bg_condition_selected);
                binding.tvConditionObvious.setTextColor(getResources().getColor(R.color.white));
                break;
        }
    }

    private void resetConditionButtons() {
        binding.tvConditionNew.setBackgroundResource(R.drawable.bg_condition_normal);
        binding.tvConditionNew.setTextColor(getResources().getColor(R.color.tblack));
        
        binding.tvConditionAlmostNew.setBackgroundResource(R.drawable.bg_condition_normal);
        binding.tvConditionAlmostNew.setTextColor(getResources().getColor(R.color.tblack));
        
        binding.tvConditionLight.setBackgroundResource(R.drawable.bg_condition_normal);
        binding.tvConditionLight.setTextColor(getResources().getColor(R.color.tblack));
        
        binding.tvConditionObvious.setBackgroundResource(R.drawable.bg_condition_normal);
        binding.tvConditionObvious.setTextColor(getResources().getColor(R.color.tblack));
    }

    private void updateShippingSelection(String shipping) {
        selectedShipping = shipping;
        
        // 重置所有按钮状态
        resetShippingButtons();
        
        // 设置选中状态
        switch (shipping) {
            case "自己填":
                binding.tvShippingSelf.setBackgroundResource(R.drawable.bg_condition_selected);
                binding.tvShippingSelf.setTextColor(getResources().getColor(R.color.white));
                binding.layoutShippingPrice.setVisibility(View.VISIBLE);
                break;
            case "包邮":
                binding.tvShippingFree.setBackgroundResource(R.drawable.bg_condition_selected);
                binding.tvShippingFree.setTextColor(getResources().getColor(R.color.white));
                binding.layoutShippingPrice.setVisibility(View.GONE);
                break;
            case "自提":
                binding.tvShippingPickup.setBackgroundResource(R.drawable.bg_condition_selected);
                binding.tvShippingPickup.setTextColor(getResources().getColor(R.color.white));
                binding.layoutShippingPrice.setVisibility(View.GONE);
                break;
        }
    }

    private void resetShippingButtons() {
        binding.tvShippingSelf.setBackgroundResource(R.drawable.bg_condition_normal);
        binding.tvShippingSelf.setTextColor(getResources().getColor(R.color.tblack));
        
        binding.tvShippingFree.setBackgroundResource(R.drawable.bg_condition_normal);
        binding.tvShippingFree.setTextColor(getResources().getColor(R.color.tblack));
        
        binding.tvShippingPickup.setBackgroundResource(R.drawable.bg_condition_normal);
        binding.tvShippingPickup.setTextColor(getResources().getColor(R.color.tblack));
    }

    private void publishBook() {
        String bookName = binding.etBookName.getText().toString().trim();
        String description = binding.etDescription.getText().toString().trim();
        String shippingPrice = binding.etShippingPrice.getText().toString().trim();
        
        // 简单验证
        if (bookName.isEmpty()) {
            Toast.makeText(this, "请输入旧书名称", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (description.isEmpty()) {
            Toast.makeText(this, "请输入旧书描述", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (selectedShipping.equals("自己填") && shippingPrice.isEmpty()) {
            Toast.makeText(this, "请输入运费", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // TODO: 实际发布逻辑
        Toast.makeText(this, "发布成功！", Toast.LENGTH_SHORT).show();
        finish();
    }
}