package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.dep.biguo.R;
import com.dep.biguo.bean.StudyToolBean;
import com.dep.biguo.databinding.StudyToolActivityBinding;
import com.dep.biguo.mvp.ui.adapter.CategoryAdapter;
import com.dep.biguo.mvp.ui.adapter.StudyToolAdapter;
import com.jess.arms.base.BaseActivity;
import com.jess.arms.di.component.AppComponent;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 学习工具页面
 */
public class StudyToolActivity extends BaseActivity implements View.OnClickListener {

    private StudyToolActivityBinding binding;
    private StudyToolAdapter adapter;
    private List<StudyToolBean> toolList;
    private CategoryAdapter categoryAdapter;
    private List<String> categoryList;
    private int currentCategory = 0; // 0:全部 1:文具 2:考试工具 3:学习用品 4:文具套装

    public static void start(Context context) {
        Intent intent = new Intent(context, StudyToolActivity.class);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        // 不需要注入
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        return R.layout.study_tool_activity;
    }



    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.study_tool_activity);
        binding.setOnClickListener(this);

        setStatusBarColorGradientStart();

        initCategoryList();

        // 先初始化adapter和RecyclerView
        binding.rvTools.setLayoutManager(new GridLayoutManager(this, 2));
        adapter = new StudyToolAdapter(new ArrayList<>());
        binding.rvTools.setAdapter(adapter);

        // 然后初始化数据
        initToolList();
        filterTools();
    }

    private void initCategoryList() {
        categoryList = new ArrayList<>(Arrays.asList("全部", "文具", "考试工具", "学习用品", "文具套装"));
        categoryAdapter = new CategoryAdapter(categoryList);
        binding.rvCategories.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
        binding.rvCategories.setAdapter(categoryAdapter);
        categoryAdapter.setOnCategoryClickListener(position -> {
            currentCategory = position;
            categoryAdapter.setSelectedPosition(position);
            filterTools();
        });
    }

    private void initToolList() {
        toolList = new ArrayList<>();
        // 模拟数据 - 使用正确的构造函数参数
        toolList.add(new StudyToolBean(1, "铅笔", "2.50", "3.00", "", 1));
        toolList.add(new StudyToolBean(2, "圆珠笔", "3.00", "4.00", "", 1));
        toolList.add(new StudyToolBean(3, "考试套装", "25.00", "30.00", "", 2));
        toolList.add(new StudyToolBean(4, "计算器", "45.00", "55.00", "", 2));
        toolList.add(new StudyToolBean(5, "学习用品套装", "35.00", "40.00", "", 3));
        toolList.add(new StudyToolBean(6, "文具套装", "20.00", "25.00", "", 4));

        adapter.setNewData(toolList);
    }

    private void filterTools() {
        List<StudyToolBean> filteredList = new ArrayList<>();
        if (currentCategory == 0) {
            filteredList.addAll(toolList);
        } else {
            for (StudyToolBean tool : toolList) {
                if (tool.getCategory() == currentCategory) {
                    filteredList.add(tool);
                }
            }
        }
        adapter.setNewData(filteredList);
    }

    private void updateCategorySelection(int categoryIndex) {
        currentCategory = categoryIndex;
        categoryAdapter.setSelectedPosition(categoryIndex);
        filterTools();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        
        if (id == R.id.ivBack) {
            finish();
        } else if (id == R.id.ivSearch) {
            // 搜索功能
            String searchText = binding.etSearch.getText().toString().trim();
            if (!searchText.isEmpty()) {
                Toast.makeText(this, "搜索: " + searchText, Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void setStatusBarColorGradientStart() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            );
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }
}
