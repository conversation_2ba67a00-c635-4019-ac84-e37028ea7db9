package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.IntegralExchangeBean;
import com.dep.biguo.bean.IntegralExchangeListBean;
import com.dep.biguo.bean.InviteTaskRewardBean;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.mvp.contract.InviteTaskRewardContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class InviteTaskRewardPresenter extends BasePresenter<InviteTaskRewardContract.Model, InviteTaskRewardContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    private InviteTaskRewardBean rewardBean;

    public InviteTaskRewardBean getRewardBean() {
        return rewardBean;
    }

    @Inject
    public InviteTaskRewardPresenter(InviteTaskRewardContract.Model model, InviteTaskRewardContract.View rootView) {
        super(model, rootView);
    }

    public void getTaskProduct(int record_id){
        mModel.getTaskProduct(record_id)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<InviteTaskRewardBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<InviteTaskRewardBean> s) {
                        if (s.isSuccess()) {
                            if(s.getData() == null){
                                mRootView.showEmptyView();
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getTaskProductSuccess(s.getData());
                                rewardBean = s.getData();
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    public void getTaskExchangeList(String goodsType){
        mModel.getTaskExchangeList(goodsType)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<IntegralExchangeListBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<IntegralExchangeListBean>> s) {
                        if (s.isSuccess()) {
                            if(s.getData().size() > 0) {
                                mRootView.showTaskExchangeDialog(s.getData());
                            }
                        }
                    }
                });
    }

    public void taskExchange(int record_id, String code){
        mModel.taskExchange(record_id, code)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<IntegralExchangeBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<IntegralExchangeBean> s) {
                        if (s.isSuccess()) {
                            mRootView.taskExchange();
                            getTaskProduct(record_id);
                        }
                    }
                });
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
