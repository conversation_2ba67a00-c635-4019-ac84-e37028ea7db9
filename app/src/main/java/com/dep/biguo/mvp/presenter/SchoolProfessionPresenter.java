package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.ProfessionSimpleBean;
import com.dep.biguo.mvp.contract.SchoolProfessionContract;
import com.dep.biguo.mvp.ui.adapter.SimpleSchoolAdapter;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;

public class SchoolProfessionPresenter extends BasePresenter<SchoolProfessionContract.Model, SchoolProfessionContract.View> {
    @Inject
    RxErrorHandler mErrorHandler;
    @Inject
    Application mApplication;
    @Inject
    ImageLoader mImageLoader;
    @Inject
    AppManager mAppManager;

    @Inject
    SimpleSchoolAdapter mSimpleSchoolAdapter;

    @Inject
    public SchoolProfessionPresenter(SchoolProfessionContract.Model model, SchoolProfessionContract.View rootView) {
        super(model, rootView);
    }


    public void getAllProfessionList(int school_id) {

        mModel.getAllProfessionList(school_id)
                .subscribeOn(Schedulers.io())

                .doOnSubscribe(disposable -> {
                    mRootView.showLoading();
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> {
                    mRootView.hideLoading();
                })
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<ProfessionSimpleBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<ProfessionSimpleBean>> s) {
                        if (s.isSuccess()) {
                            mSimpleSchoolAdapter.setNewData(s.getData());
                        }else {
                            mRootView.showMessage(s.getResult_info());
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                    }
                });
    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
    }
}
