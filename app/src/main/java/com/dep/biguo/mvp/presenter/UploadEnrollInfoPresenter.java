package com.dep.biguo.mvp.presenter;

import static com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity.NAME;
import static com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity.IDENTITY_CARD_NUMBER;
import static com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity.ACADEMIC_CERTIFICATE;
import static com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity.ACADEMIC_CERTIFICATE_PROOF;
import static com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity.IDENTITY_CARD_FRONT;
import static com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity.IDENTITY_CARD_REVERSE;
import static com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity.PORTRAIT;
import static com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity.RESIDENCE_PERMIT_FRONT;
import static com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity.RESIDENCE_PERMIT_REVERSE;
import static com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity.SOCIAL_SECURITY_CARD_FRONT;
import static com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity.SOCIAL_SECURITY_CARD_REVERSE;
import static com.dep.biguo.mvp.ui.activity.UploadEnrollInfoActivity.SOCIAL_SECURITY_PAY_PROOF;

import android.app.Application;
import android.text.TextUtils;

import com.biguo.utils.util.LogUtil;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.UploadEnrollInfoBean;
import com.dep.biguo.mvp.contract.UploadEnrollInfoContract;
import com.biguo.utils.widget.LoadingDialog;
import com.dep.biguo.utils.AESEncrypt;
import com.dep.biguo.utils.RSAEncrypt;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;


@ActivityScope
public class UploadEnrollInfoPresenter extends BasePresenter<UploadEnrollInfoContract.Model, UploadEnrollInfoContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public UploadEnrollInfoPresenter(UploadEnrollInfoContract.Model model, UploadEnrollInfoContract.View rootView) {
        super(model, rootView);
    }

    public void getData(){
        String[] keyPair;
        String publicKey;
        try {
            keyPair = RSAEncrypt.genKeyPair();
            if(keyPair[0].startsWith("\n")){
                keyPair[0] = keyPair[0].substring(1);
            }
            if(keyPair[0].endsWith("\n")){
                keyPair[0] = keyPair[0].substring(0, keyPair[0].length()-1);
            }

            publicKey = URLEncoder.encode(keyPair[0], "utf-8");
        } catch (UnsupportedEncodingException  e) {
            mRootView.showMessage("编码失败");
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            mRootView.showMessage("秘钥生成失败，请联系客服");
            throw new RuntimeException(e);
        }
        mModel.getData(publicKey)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<UploadEnrollInfoBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<UploadEnrollInfoBean> response) {
                        if (response.isSuccess()) {
                            try {
                                UploadEnrollInfoBean infoBean = response.getData();
                                if(!TextUtils.isEmpty(infoBean.getKey())) {
                                    String aesKey = RSAEncrypt.decryptByPrivateKey(infoBean.getKey(), keyPair[1]);
                                    infoBean.setKey(aesKey);
                                    if(!TextUtils.isEmpty(infoBean.getName())) {
                                        infoBean.setName(AESEncrypt.decrypt(infoBean.getName(), aesKey));
                                    }
                                    if(!TextUtils.isEmpty(infoBean.getIdentity_card_number())) {
                                        infoBean.setIdentity_card_number(AESEncrypt.decrypt(infoBean.getIdentity_card_number(), aesKey));
                                    }
                                }

                                mRootView.getDataSuccess(response.getData());
                            } catch (Exception e) {
                                mRootView.showMessage("解密失败");
                            }
                        }
                    }
                });
    }


    /**验证参数
     * @param urlMap
     * @return
     */
    public static String verify(Map<String, String> urlMap) {
        //验证checkValueMap中必填参数，参数验证顺序不是按照添加顺序验证的，可参考put语句后面的注释
        Map<String, String> checkValueMap = new HashMap<>();
        checkValueMap.put(NAME, "姓名不能为空");//4
        checkValueMap.put(IDENTITY_CARD_NUMBER, "身份证号码不能为空");//5
        checkValueMap.put(PORTRAIT, "蓝底照片不能为空");//3
        checkValueMap.put(IDENTITY_CARD_FRONT, "身份证正面不能为空");//2
        checkValueMap.put(IDENTITY_CARD_REVERSE, "身份证反面不能为空");//1
        for (String key : checkValueMap.keySet()) {
            if (TextUtils.isEmpty(urlMap.get(key))) {
                return checkValueMap.get(key);
            }
        }

        if(TextUtils.isEmpty(urlMap.get(ACADEMIC_CERTIFICATE)) && TextUtils.isEmpty(urlMap.get(ACADEMIC_CERTIFICATE_PROOF))){
            return "请上传任一类型学历证书";
        }

        //异地证明可以提交的情况
        if (!TextUtils.isEmpty(urlMap.get(SOCIAL_SECURITY_CARD_FRONT)) && !TextUtils.isEmpty(urlMap.get(SOCIAL_SECURITY_CARD_REVERSE))) {
            return "";//社保卡正反面都有
        }else if (!TextUtils.isEmpty(urlMap.get(RESIDENCE_PERMIT_FRONT)) && !TextUtils.isEmpty(urlMap.get(RESIDENCE_PERMIT_REVERSE))) {
            return "";//居住证正反面都有
        }else if(!TextUtils.isEmpty(urlMap.get(SOCIAL_SECURITY_PAY_PROOF))) {
            return "";//社保缴纳证明有
        }

        //异地证明不能提交的情况
        if (TextUtils.isEmpty(urlMap.get(SOCIAL_SECURITY_CARD_FRONT)) && !TextUtils.isEmpty(urlMap.get(SOCIAL_SECURITY_CARD_REVERSE))) {
            return "社保卡正面不能为空";
        }else if(!TextUtils.isEmpty(urlMap.get(SOCIAL_SECURITY_CARD_FRONT)) && TextUtils.isEmpty(urlMap.get(SOCIAL_SECURITY_CARD_REVERSE))){
            return "社保卡反面不能为空";
        }else if (TextUtils.isEmpty(urlMap.get(RESIDENCE_PERMIT_FRONT)) && !TextUtils.isEmpty(urlMap.get(RESIDENCE_PERMIT_REVERSE))) {
            return "居住证正面不能为空";
        }else if(!TextUtils.isEmpty(urlMap.get(RESIDENCE_PERMIT_FRONT)) && TextUtils.isEmpty(urlMap.get(RESIDENCE_PERMIT_REVERSE))){
            return "居住证反面不能为空";
        }else if (TextUtils.isEmpty(urlMap.get(SOCIAL_SECURITY_PAY_PROOF))) {
            return "请上传任一类型异地证明证件";
        }
        return "";
    }

    public void uploadInfo(Map<String, String> urlMap, String aesKey) {
        LogUtil.d("dddd", urlMap);
        String errorMessage = verify(urlMap);
        if(!TextUtils.isEmpty(errorMessage)){
            mRootView.showMessage(errorMessage);
            return;
        }

        mModel.uploadInfo(urlMap, aesKey)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse response) {
                        if (response.isSuccess()) {
                            mRootView.showMessage("上传资料成功");
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
