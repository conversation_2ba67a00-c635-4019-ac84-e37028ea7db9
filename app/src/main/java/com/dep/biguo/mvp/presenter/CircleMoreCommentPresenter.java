package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CircleActionBean;
import com.dep.biguo.bean.CircleBean;
import com.dep.biguo.bean.CircleCommentBean;
import com.dep.biguo.mvp.contract.CircleMoreCommentContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import org.simple.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class CircleMoreCommentPresenter extends BasePresenter<CircleMoreCommentContract.Model, CircleMoreCommentContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public CircleMoreCommentPresenter(CircleMoreCommentContract.Model model, CircleMoreCommentContract.View rootView) {
        super(model, rootView);
    }

    public void getItemComment(int comment_id, int page) {
        mModel.getItemComment(comment_id, page)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<CircleCommentBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<CircleCommentBean>> response) {
                        if (response.isSuccess()) {
                            if(AppUtil.isEmpty(response.getData())){
                                mRootView.showSuccessView();
                                mRootView.getItemCommentSuccess(new ArrayList<>());
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getItemCommentSuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    /**评论动态
     * @param posts_id  动态ID
     * @param comment_id 被评论/被回复 的ID
     * @param comment 评论
     */
    public void comment(CircleBean.Moment moment, int comment_id, String comment) {
        mModel.comment(moment.getPosts_id(), comment_id, comment)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<CircleCommentBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<CircleCommentBean> s) {
                        if(s.isSuccess()) {
                            mRootView.commentSuccess(s.getData());
                        }
                    }
                });
    }
    /**举报评论
     * @param comment_id 评论ID
     * @param reason 举报原因
     */
    public void reportComment(int comment_id, String reason) {
        mModel.reportComment(comment_id, reason)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if(s.isSuccess()) {
                            mRootView.showMessage("举报成功");
                        }
                    }
                });
    }

    /**删除评论
     * @param moment       动态
     * @param comment      评论
     * @param childComment 子评论
     */
    public void deleteComment(CircleBean.Moment moment, CircleCommentBean comment, CircleCommentBean childComment){
        CircleCommentBean actionComment = childComment == null ? comment: childComment;
        mModel.deleteComment(actionComment.getComment_id())
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if(s.isSuccess()) {
                            CircleActionBean action = new CircleActionBean(moment, comment, childComment, CircleActionBean.COMMENT_DELETE);
                            EventBus.getDefault().post(action, EventBusTags.CIRCLE_REFRESH_COMMENT_ITEM);
                            mRootView.deleteComment(comment, childComment);
                        }
                    }
                });
    }

    /**评论的点赞 计数
     * @param moment       动态
     * @param comment      评论
     * @param childComment 子评论
     * @param isNotify     是否发出一个通知
     */
    public void commentGood(CircleBean.Moment moment, CircleCommentBean comment, CircleCommentBean childComment, boolean isNotify) {
        CircleCommentBean actionComment = childComment == null ? comment: childComment;
        mModel.commentGood(actionComment.getComment_id(), 1 - actionComment.getIs_like())
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse s) {
                        if(isNotify) {
                            CircleActionBean action = new CircleActionBean(moment, comment, childComment, CircleActionBean.COMMENT_GOOD);
                            EventBus.getDefault().post(action, EventBusTags.CIRCLE_REFRESH_COMMENT_ITEM);
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
