package com.dep.biguo.mvp.presenter;

import android.app.Application;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.OnLifecycleEvent;
import android.text.TextUtils;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CKStudyDataStatusBean;
import com.dep.biguo.mvp.contract.CKStudydataUploadContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import javax.inject.Inject;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;
import me.jessyan.rxerrorhandler.handler.RetryWithDelay;

@ActivityScope
public class CKStudydataUploadPresenter extends BasePresenter<CKStudydataUploadContract.Model, CKStudydataUploadContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public CKStudydataUploadPresenter(CKStudydataUploadContract.Model model, CKStudydataUploadContract.View rootView) {
        super(model, rootView);
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    public void getStatus() {
        mModel.study_status_data(mRootView.getDataTypeParam(mRootView.getType()))
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<CKStudyDataStatusBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<CKStudyDataStatusBean> response) {
                        if (response.isSuccess()) {
                            mRootView.setData(mRootView.getType(), response.getData());
                        }
                    }
                });
    }

    public void upload() {
        String typeParam = "";
        String path = "";
        int type = mRootView.getType();
        switch (mRootView.getType()) {
            // 身份证
            case 1:
                if (TextUtils.isEmpty(mRootView.getImagePath1()) || TextUtils.isEmpty(mRootView.getImagePath2())) {
                    mRootView.showMessage("请完善图片");
                    return;
                }
                uploadIdcard();
                return;
            // 蓝底照片
            case 2:
                typeParam = mRootView.getTypeParam(type);
                path = mRootView.getImagePath3();
                break;
            // 学历证书、三证、技能证书
            case 3:
            case 4:
            case 5:
                typeParam = mRootView.getTypeParam(type);
                path = mRootView.getImagePath1();
                break;
        }
        if (TextUtils.isEmpty(path)) {
            mRootView.showMessage("请完善图片");
            return;
        }
        uploadOther(typeParam, path);
    }

    private void uploadOther(String type, String path) {
        mModel.study_status_upload(type, path)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse baseResponse) {
                        if (baseResponse.isSuccess()) {
                            mRootView.uploadSuccess();
                        }
                    }
                });
    }

    // 上传身份证
    private void uploadIdcard() {
        Observable<BaseResponse> idcardObservable1 = mModel.study_status_upload(mRootView.getTypeParam(0), mRootView.getImagePath1());
        Observable<BaseResponse> idcardObservable2 = mModel.study_status_upload(mRootView.getTypeParam(1), mRootView.getImagePath2());
        Observable.zip(idcardObservable1, idcardObservable2, (baseResponse, baseResponse2) -> {
            if (baseResponse.isFail() || baseResponse2.isFail())
                return false;
            return true;
        }).subscribeOn(Schedulers.io())

                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<Boolean>(mErrorHandler) {
                    @Override
                    public void onNext(Boolean aBoolean) {
                        if (aBoolean) {
                            mRootView.uploadSuccess();
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
