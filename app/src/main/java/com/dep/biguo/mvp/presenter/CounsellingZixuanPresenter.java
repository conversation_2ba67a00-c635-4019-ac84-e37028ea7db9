package com.dep.biguo.mvp.presenter;

import android.app.Application;

import com.dep.biguo.bean.BaseResponse;
import com.dep.biguo.bean.CounsellingDazikaoBean;
import com.dep.biguo.bean.CounsellingZixuanBean;
import com.dep.biguo.bean.CounsellingZixuanCourseBean;
import com.dep.biguo.mvp.contract.CounsellingZixuanContract;
import com.biguo.utils.widget.LoadingDialog;
import com.jess.arms.di.scope.ActivityScope;
import com.jess.arms.http.imageloader.ImageLoader;
import com.jess.arms.integration.AppManager;
import com.jess.arms.mvp.BasePresenter;
import com.jess.arms.utils.RxLifecycleUtils;

import java.util.List;

import javax.inject.Inject;

import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.rxerrorhandler.core.RxErrorHandler;
import me.jessyan.rxerrorhandler.handler.ErrorHandleSubscriber;

@ActivityScope
public class CounsellingZixuanPresenter extends BasePresenter<CounsellingZixuanContract.Model, CounsellingZixuanContract.View> {
    @Inject RxErrorHandler mErrorHandler;
    @Inject Application mApplication;
    @Inject ImageLoader mImageLoader;
    @Inject AppManager mAppManager;

    @Inject
    public CounsellingZixuanPresenter(CounsellingZixuanContract.Model model, CounsellingZixuanContract.View rootView) {
        super(model, rootView);
    }
    /**获取介绍详情
     */
    public void getCounsellingZixuanDetail(String class_type) {
        mModel.getCounsellingZixuanDetail(class_type)
                .subscribeOn(Schedulers.io())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<CounsellingZixuanBean>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<CounsellingZixuanBean> response) {
                        if(response.isSuccess()){
                            if(response.getData() == null){
                                mRootView.showEmptyView();
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getCounsellingZixuanDetailSuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        super.onError(t);
                        mRootView.showErrorView(t);
                    }
                });
    }

    /**获取可自选的课程
     */
    public void getCounsellingZixuanCourse(String class_type) {
        mModel.getCounsellingZixuanCourse(class_type)
                .subscribeOn(Schedulers.io())
                .doOnSubscribe(disposable -> mRootView.showLoadingDialog())
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .doFinally(() -> mRootView.hideLoadingDialog())
                .compose(RxLifecycleUtils.bindToLifecycle(mRootView))
                .subscribe(new ErrorHandleSubscriber<BaseResponse<List<CounsellingZixuanCourseBean>>>(mErrorHandler) {
                    @Override
                    public void onNext(BaseResponse<List<CounsellingZixuanCourseBean>> response) {
                        if(response.isSuccess()){
                            if(response.getData() == null){
                                mRootView.showEmptyView();
                            }else {
                                mRootView.showSuccessView();
                                mRootView.getCounsellingZixuanCourseSuccess(response.getData());
                            }
                        }else {
                            mRootView.showErrorView(null);
                        }
                    }
                });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        this.mErrorHandler = null;
        this.mAppManager = null;
        this.mImageLoader = null;
        this.mApplication = null;
        LoadingDialog.hideLoadingDialog();
    }
}
