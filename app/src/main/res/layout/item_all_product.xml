<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:background="@color/white"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp">

        <!-- 商品图片 -->
        <ImageView
            android:id="@+id/ivProductImage"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:background="@drawable/bg_round_8_gray"
            android:scaleType="centerCrop"
            tools:src="@color/theme_alpha_10" />

        <!-- 商品标签 -->
        <TextView
            android:id="@+id/tvProductTag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/bg_gradient_red"
            android:paddingStart="6dp"
            android:paddingTop="2dp"
            android:paddingEnd="6dp"
            android:paddingBottom="2dp"
            android:text="全新"
            android:textColor="@color/white"
            android:textSize="10sp" />

        <!-- 商品名称 -->
        <TextView
            android:id="@+id/tvProductName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="这里是商品名称\n多展示两行文字"
            android:textColor="@color/tblack"
            android:textSize="12sp" />

        <!-- 价格 -->
        <TextView
            android:id="@+id/tvPrice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="¥128.00"
            android:textColor="@color/theme"
            android:textSize="14sp"
            android:textStyle="bold" />

        <!-- 销量 -->
        <TextView
            android:id="@+id/tvSales"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="已售8000+"
            android:textColor="@color/tblack3"
            android:textSize="10sp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
