<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="140dp"
    android:layout_height="wrap_content"
    android:layout_marginEnd="12dp"
    android:background="@color/white"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp">

        <!-- 商品图片容器 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="100dp">

            <ImageView
                android:id="@+id/ivProductImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/bg_round_8_gray"
                android:scaleType="centerCrop"
                tools:src="@color/theme_alpha_10" />

            <!-- TOP标签 -->
            <TextView
                android:id="@+id/tvTopLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|start"
                android:layout_margin="4dp"
                android:background="@drawable/bg_gradient_red"
                android:paddingStart="6dp"
                android:paddingTop="2dp"
                android:paddingEnd="6dp"
                android:paddingBottom="2dp"
                android:text="月销量TOP1"
                android:textColor="@color/white"
                android:textSize="10sp" />

        </FrameLayout>

        <!-- 商品名称 -->
        <TextView
            android:id="@+id/tvProductName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="笔果AI学习机"
            android:textColor="@color/tblack"
            android:textSize="12sp" />

        <!-- 价格 -->
        <TextView
            android:id="@+id/tvPrice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="¥2500.00"
            android:textColor="@color/theme"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
