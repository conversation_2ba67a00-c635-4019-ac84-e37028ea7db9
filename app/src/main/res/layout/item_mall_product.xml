<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginEnd="12dp"
    android:layout_marginBottom="16dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:cardUseCompatPadding="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 商品图片容器 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="140dp">

            <!-- 商品图片 -->
            <ImageView
                android:id="@+id/ivProductImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/bg_gray"
                android:scaleType="centerCrop" />

            <!-- 成色标签 -->
            <TextView
                android:id="@+id/tvConditionTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|start"
                android:layout_margin="8dp"
                android:background="@drawable/bg_condition_tag"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:text="九成新"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:textStyle="bold" />

            <!-- 急售标签 -->
            <TextView
                android:id="@+id/tvUrgentTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|end"
                android:layout_margin="8dp"
                android:background="@drawable/bg_urgent_tag"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:text="急售"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:textStyle="bold"
                android:visibility="gone" />

        </FrameLayout>

        <!-- 商品信息区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- 商品标题 -->
            <TextView
                android:id="@+id/tvProductTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="高等数学 第七版 上册"
                android:textColor="@color/tblack"
                android:textSize="14sp"
                android:textStyle="bold" />

            <!-- 副标题 -->
            <TextView
                android:id="@+id/tvSubTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="同济大学出版社 | 专升本教材"
                android:textColor="@color/tblack3"
                android:textSize="12sp"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- 价格和位置信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <!-- 价格区域 -->
                <TextView
                    android:id="@+id/tvPrice"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="¥28 ¥45"
                    android:textSize="16sp"
                    android:gravity="center_vertical" />

                <!-- 位置信息 -->
                <TextView
                    android:id="@+id/tvLocation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="昆明"
                    android:textColor="@color/tblack3"
                    android:textSize="11sp"
                    android:drawableStart="@drawable/icon_location_small"
                    android:drawablePadding="2dp"
                    android:gravity="center_vertical" />

            </LinearLayout>

            <!-- 发布时间 -->
            <TextView
                android:id="@+id/tvPublishTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:text="2小时前"
                android:textColor="@color/tblack3"
                android:textSize="11sp"
                android:gravity="end" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView> 