<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable name="onClickListener" type="com.dep.biguo.mvp.ui.activity.PublishBookActivity"/>
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/bg_gray"
        android:fitsSystemWindows="true">

        <!-- 顶部标题栏 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="14dp"
            android:paddingBottom="16dp"
            >

            <!-- 返回按钮 -->
            <ImageView
                android:id="@+id/ivBack"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:src="@drawable/arrow_back"
                android:onClick="@{onClickListener.onClick}"
                android:padding="2dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                app:tint="@color/tblack" />

            <!-- 标题 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="发布旧书"
                android:textColor="@color/tblack"
                android:textSize="18sp"
                android:textStyle="bold" />

        </RelativeLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fillViewport="false">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingBottom="20dp">

                <!-- 旧书名称 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:layout_marginTop="8dp">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:text="旧书名称"
                        android:textColor="@color/tblack"
                        android:textSize="16sp" />

                    <EditText
                        android:id="@+id/etBookName"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@null"
                        android:text="2025专升本英语教材人教版"
                        android:textColor="@color/tblack"
                        android:textSize="16sp"
                        android:singleLine="true" />

                </LinearLayout>

                <!-- 描述信息和添加图片 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:layout_marginTop="8dp">

                    <EditText
                        android:id="@+id/etDescription"
                        android:layout_width="match_parent"
                        android:layout_height="120dp"
                        android:background="@null"
                        android:gravity="top"
                        android:hint="简单描述下您需发布的旧书信息"
                        android:textColor="@color/tblack"
                        android:textColorHint="@color/tblack3"
                        android:textSize="14sp"
                        android:inputType="textMultiLine"
                        android:maxLines="6" />

                    <!-- 添加图片按钮 -->
                    <FrameLayout
                        android:id="@+id/layoutAddImage"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_marginTop="12dp"
                        android:background="@drawable/bg_add_image"
                        android:onClick="@{onClickListener.onClick}">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:text="+"
                            android:textColor="@color/tblack3"
                            android:textSize="32sp" />

                    </FrameLayout>

                </LinearLayout>

                <!-- 售价 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:layout_marginTop="8dp">

                    <TextView
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:text="售价"
                        android:textColor="@color/tblack"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="¥ 0.00"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:layout_weight="1" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/arrow_right"
                        android:onClick="@{onClickListener.onClick}"
                        app:tint="@color/tblack3" />

                </LinearLayout>

                <!-- 原价 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp">

                    <TextView
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:text="原价"
                        android:textColor="@color/tblack"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="¥ 0.00"
                        android:textColor="@color/red"
                        android:textSize="16sp"
                        android:layout_weight="1" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/arrow_right"
                        android:onClick="@{onClickListener.onClick}"
                        app:tint="@color/tblack3" />

                </LinearLayout>

                <!-- 成色 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:orientation="vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:layout_marginTop="8dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="成色"
                        android:textColor="@color/tblack"
                        android:textSize="16sp"
                        android:layout_marginBottom="12dp" />

                    <!-- Wrap conditions in a HorizontalScrollView -->
                    <HorizontalScrollView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:overScrollMode="never"
                        android:scrollbars="none">
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvConditionNew"
                                android:layout_width="wrap_content"
                                android:layout_height="36dp"
                                android:layout_marginEnd="8dp"
                                android:background="@drawable/bg_condition_selected"
                                android:gravity="center"
                                android:text="全新"
                                android:textColor="@color/white"
                                android:textSize="14sp"
                                android:onClick="@{onClickListener.onClick}"
                                android:paddingStart="12dp"
                                android:paddingEnd="12dp" />

                            <TextView
                                android:id="@+id/tvConditionAlmostNew"
                                android:layout_width="wrap_content"
                                android:layout_height="36dp"
                                android:layout_marginEnd="8dp"
                                android:background="@drawable/bg_condition_normal"
                                android:gravity="center"
                                android:text="几乎全新"
                                android:textColor="@color/tblack"
                                android:textSize="14sp"
                                android:onClick="@{onClickListener.onClick}"
                                android:paddingStart="12dp"
                                android:paddingEnd="12dp" />

                            <TextView
                                android:id="@+id/tvConditionLight"
                                android:layout_width="wrap_content"
                                android:layout_height="36dp"
                                android:layout_marginEnd="8dp"
                                android:background="@drawable/bg_condition_normal"
                                android:gravity="center"
                                android:onClick="@{onClickListener.onClick}"
                                android:text="轻微使用痕迹"
                                android:textColor="@color/tblack"
                                android:textSize="14sp"
                                android:paddingStart="12dp"
                                android:paddingEnd="12dp" />

                            <TextView
                                android:id="@+id/tvConditionObvious"
                                android:layout_width="wrap_content"
                                android:layout_height="36dp"
                                android:background="@drawable/bg_condition_normal"
                                android:gravity="center"
                                android:text="明显使用痕迹"
                                android:textColor="@color/tblack"
                                android:textSize="14sp"
                                android:onClick="@{onClickListener.onClick}"
                                android:paddingStart="12dp"
                                android:paddingEnd="12dp" />

                        </LinearLayout>
                    </HorizontalScrollView>

                </LinearLayout>

                <!-- 邮寄(运费) -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:background="@color/white"
                    android:orientation="vertical"
                    android:paddingStart="16dp"
                    android:paddingTop="16dp"
                    android:paddingEnd="16dp"
                    android:paddingBottom="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="邮寄(运费)"
                        android:textColor="@color/tblack"
                        android:textSize="16sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvShippingSelf"
                            android:layout_width="0dp"
                            android:layout_height="36dp"
                            android:layout_marginEnd="12dp"
                            android:layout_weight="1"
                            android:background="@drawable/bg_condition_selected"
                            android:gravity="center"
                            android:onClick="@{onClickListener.onClick}"
                            android:text="自己填"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvShippingFree"
                            android:layout_width="0dp"
                            android:layout_height="36dp"
                            android:layout_marginEnd="12dp"
                            android:layout_weight="1"
                            android:background="@drawable/bg_condition_normal"
                            android:gravity="center"
                            android:onClick="@{onClickListener.onClick}"
                            android:text="包邮"
                            android:textColor="@color/tblack"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvShippingPickup"
                            android:layout_width="0dp"
                            android:layout_height="36dp"
                            android:layout_weight="1"
                            android:background="@drawable/bg_condition_normal"
                            android:gravity="center"
                            android:onClick="@{onClickListener.onClick}"
                            android:text="自提"
                            android:textColor="@color/tblack"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <!-- 运费输入框 -->
                    <LinearLayout
                        android:id="@+id/layoutShippingPrice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="¥"
                            android:textColor="@color/tblack"
                            android:textSize="24sp"
                            android:textStyle="bold" />

                        <EditText
                            android:id="@+id/etShippingPrice"
                            android:layout_width="120dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:background="@null"
                            android:hint="请输入"
                            android:inputType="numberDecimal"
                            android:textColor="@color/tblack"
                            android:textColorHint="@color/tblack3"
                            android:textSize="24sp"
                            android:textStyle="bold" />



                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </ScrollView>

        <!-- 底部发布按钮 -->
        <TextView
            android:id="@+id/btnPublish"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:layout_margin="16dp"
            android:background="@drawable/bg_gradient_red"
            android:gravity="center"
            android:text="发布"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:onClick="@{onClickListener.onClick}" />

    </LinearLayout>
</layout>