<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.fragment.MallHomeFragment" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:fitsSystemWindows="false"
        android:orientation="vertical">

        <!-- 顶部红色区域 -->
        <LinearLayout
            android:id="@+id/mallHomeTopLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_gradient_red"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="15dp">

            <com.dep.biguo.widget.StatusBarView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <!-- 标题 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginBottom="8dp"
                android:fontFamily="@font/hanzhenguangbiao"
                android:text="商城"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- 搜索框 -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="@drawable/bg_round_50_white">

                <EditText
                    android:id="@+id/etSearch"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="60dp"
                    android:background="@null"
                    android:hint="考题押密"
                    android:imeOptions="actionSearch"
                    android:singleLine="true"
                    android:textColorHint="@color/tblack3"
                    android:textSize="14sp" />

                <ImageView
                    android:id="@+id/ivSearchIcon"
                    android:layout_width="60dp"
                    android:layout_height="36dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="2dp"
                    android:background="@drawable/bg_capsule_yellow"
                    android:padding="10dp"
                    android:src="@drawable/icon_search"
                    app:tint="#000000" />

            </RelativeLayout>

        </LinearLayout>

        <com.dep.biguo.widget.SmartRefreshLayout
            android:id="@+id/swipeLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fillViewport="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- 横幅广告 -->
                    <ImageView
                        android:id="@+id/ivBanner"
                        android:layout_width="match_parent"
                        android:layout_height="120dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="15dp"
                        android:layout_marginEnd="15dp"
                        android:background="@drawable/bg_round_8_gray"
                        android:scaleType="centerCrop"
                        tools:src="@color/theme_alpha_10" />

                    <!-- 功能按钮区域 -->
                    <GridLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="20dp"
                        android:layout_marginEnd="15dp"
                        android:columnCount="4"
                        android:rowCount="1">

                        <!-- 学习工具 -->
                        <LinearLayout
                            android:id="@+id/layoutStudyTools"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_columnWeight="1"
                            android:gravity="center"
                            android:onClick="@{onClickListener.onClick}"
                            android:orientation="vertical"
                            android:padding="10dp">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:background="@drawable/bg_circle_blue"
                                android:padding="12dp"
                                android:src="@drawable/icon_tools"
                                app:tint="@color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="学习工具"
                                android:textColor="@color/tblack"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <!-- 二手教材 -->
                        <LinearLayout
                            android:id="@+id/layoutUsedBooks"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_columnWeight="1"
                            android:gravity="center"
                            android:onClick="@{onClickListener.onClick}"
                            android:orientation="vertical"
                            android:padding="10dp">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:background="@drawable/bg_circle_pink"
                                android:padding="12dp"
                                android:src="@drawable/icon_book"
                                app:tint="@color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="二手教材"
                                android:textColor="@color/tblack"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <!-- 助农专区 -->
                        <LinearLayout
                            android:id="@+id/layoutStudyArea"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_columnWeight="1"
                            android:gravity="center"
                            android:onClick="@{onClickListener.onClick}"
                            android:orientation="vertical"
                            android:padding="10dp">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:background="@drawable/bg_circle_green"
                                android:padding="12dp"
                                android:src="@drawable/icon_study"
                                app:tint="@color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="助农专区"
                                android:textColor="@color/tblack"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <!-- 收货地址 -->
                        <LinearLayout
                            android:id="@+id/layoutAddress"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_columnWeight="1"
                            android:gravity="center"
                            android:onClick="@{onClickListener.onClick}"
                            android:orientation="vertical"
                            android:padding="10dp">

                            <ImageView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                android:background="@drawable/bg_circle_orange"
                                android:padding="12dp"
                                android:src="@drawable/icon_location"
                                app:tint="@color/white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="收货地址"
                                android:textColor="@color/tblack"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </GridLayout>

                    <!-- 学习套装区域 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="25dp"
                        android:layout_marginEnd="15dp"
                        android:orientation="horizontal">

                        <!-- 包装容器，负责占据空间 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:background="@drawable/yellow_underline_bg"
                                android:text="学习套装"
                                android:textColor="@color/tblack"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvStudyPackageMore"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableEnd="@drawable/arrow_gray_right"
                            android:drawablePadding="4dp"
                            android:gravity="center_vertical"
                            android:onClick="@{onClickListener.onClick}"
                            android:text="更多"
                            android:textColor="@color/tblack3"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <!-- 学习套装商品列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvStudyPackages"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="15dp"
                        android:nestedScrollingEnabled="false"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="2" />

                    <!-- 书籍教材区域 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="25dp"
                        android:layout_marginEnd="15dp"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:background="@drawable/yellow_underline_bg"
                                android:text="书籍教材"
                                android:textColor="@color/tblack"
                                android:textSize="16sp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvBooksMore"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableEnd="@drawable/arrow_gray_right"
                            android:drawablePadding="4dp"
                            android:gravity="center_vertical"
                            android:onClick="@{onClickListener.onClick}"
                            android:text="更多"
                            android:textColor="@color/tblack3"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <!-- 书籍教材商品列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvBooks"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="15dp"
                        android:layout_marginBottom="20dp"
                        android:nestedScrollingEnabled="false"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="2" />

                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

        </com.dep.biguo.widget.SmartRefreshLayout>

    </LinearLayout>

</layout>
