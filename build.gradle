// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: "config.gradle"
apply from: 'thirdparty-lib/config.gradle'

buildscript {
    
    repositories {
        // GitHub 相关依赖优先从 jitpack.io 获取
        maven { url "https://jitpack.io" }
        google()
        mavenCentral()

        //华为厂商通道
        maven{ url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        maven { 
            url 'https://developer.huawei.com/repo/'
            allowInsecureProtocol = true
        }
        // 字节跳动仓库 - 放在最后
        maven { 
            url 'https://artifact.bytedance.com/repository/Volcengine/'
            allowInsecureProtocol = true
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.0.2'

        //配置Bugly符号表
        classpath 'com.tencent.bugly:symtabfileuploader:2.2.1'
        //华为厂商通道
        classpath 'com.huawei.agconnect:agcp:1.9.1.301'
        //realm数据库的插件过低可能导致java.lang.NoClassDefFoundError错误
        classpath "io.realm:realm-gradle-plugin:10.14.0"
        //保利威
        //classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.3.72"
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.5.20'

    }
}

allprojects {
    repositories {
        // GitHub 相关依赖优先从 jitpack.io 获取 - 最高优先级
        maven { url "https://jitpack.io" }
        google()
        mavenCentral()
        jcenter()
        // 支付宝 SDK AAR 包所需的配置
        flatDir {
            dirs 'libs'
        }
        maven {
            allowInsecureProtocol true
            url 'http://maven.aliyun.com/repository/google'
        }
        maven { url 'https://maven.aliyun.com/repository/public' }

        //阿里云镜像仓库
        maven { url "https://maven.aliyun.com/repository/releases" }

        // CC线上maven
        maven {
            allowInsecureProtocol true
            url 'http://nexus-app.bokecc.com/repository/sdk-group/'
        }
//        maven { url 'https://www.jitpack.io' }
        maven {
            allowInsecureProtocol true
            url "http://maven.aliyun.com/nexus/content/repositories/releases"
        }

        maven {
            allowInsecureProtocol true
            url 'http://4thline.org/m2'
        }
        flatDir { dirs 'src/main/libs' }

        //华为厂商通道
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { 
            url 'https://developer.huawei.com/repo/'
            allowInsecureProtocol = true
        }

        //巨量引擎 - 暂时注释掉以解决 rxpermissions 依赖问题
        //maven { 
        //    url 'https://artifact.bytedance.com/repository/Volcengine/'
        //    allowInsecureProtocol = true
        //}

        //保利威阿里云效
        maven {
            credentials {
                username '609cc5623a10edbf36da9615'
                password 'EbkbzTNHRJ=P'
            }
            url 'https://packages.aliyun.com/maven/repository/2102846-release-8EVsoM/'
        }

        //穿山甲融合广告的需要的Gromore maven 仓库 - 已移动到文件末尾
        maven {
            url 'https://maven.pkg.github.com/CarGuo/GSYVideoPlaye'

            // You can also use your own GitHub account and token
            // For convenience, I have provided a token for an infrequently used account here
            credentials {
                username = "axcda"
                password = "****************************************"
            }
        }
        maven{ url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        
        // 字节跳动仓库 - 放在最后，确保其他仓库优先级更高
        maven { 
            url 'https://artifact.bytedance.com/repository/Volcengine/'
            allowInsecureProtocol = true
        }
        maven {
            url "https://artifact.bytedance.com/repository/pangle"
            allowInsecureProtocol = true
        }

    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
